<g:render template="/${session['entryController']}/navheader_new"></g:render>

<style>
    body {
        background: #fff !important;
    }
    main {
        min-height: 75vh;
        margin-top: 4rem;
    }
    .ws_container {
        width: calc(100% - 30%);
        margin: 0 auto;
    }
    .liveMockTests {
        margin-top: 2rem;
        margin-bottom: 3rem;
    }
    .liveMockTests__title {
        font-size: 1.7rem;
        margin-bottom: 2rem;
        text-align: center;
    }
    .mockTestCard {
        background: #fff;
        box-shadow: 0 3px 4px rgba(0, 0, 0, 0.15);
        border-radius: 10px;
        border: 1px solid rgba(0, 0, 0, 0.08);
        padding: 20px;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
        border-left: 4px solid;
    }
    .mockTestCard:hover {
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
        transform: translateY(-2px);
    }
    .mockTestCard.active {
        border-left-color: #28a745;
    }
    .mockTestCard.completed {
        border-left-color: #6c757d;
    }
    .mockTestCard.upcoming {
        border-left-color: #007bff;
    }
    .mockTestCard__header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 15px;
    }
    .mockTestCard__title {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 5px;
        color: #333;
    }
    .mockTestCard__status {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
        text-transform: uppercase;
    }
    .status-active {
        background: #d4edda;
        color: #155724;
    }
    .status-completed {
        background: #f8d7da;
        color: #721c24;
    }
    .status-upcoming {
        background: #d1ecf1;
        color: #0c5460;
    }
    .mockTestCard__details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
        margin-bottom: 15px;
    }
    .detail-item {
        display: flex;
        align-items: center;
        gap: 8px;
    }
    .detail-item i {
        color: #6c757d;
        width: 16px;
    }
    .detail-item span {
        font-size: 0.9rem;
        color: #495057;
    }
    .mockTestCard__actions {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
    }
    .btn-test {
        padding: 8px 16px;
        border: none;
        border-radius: 5px;
        font-size: 0.9rem;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 5px;
    }
    .btn-start {
        background: #28a745;
        color: white;
    }
    .btn-start:hover {
        background: #218838;
        color: white;
    }
    .btn-view {
        background: #007bff;
        color: white;
    }
    .btn-view:hover {
        background: #0056b3;
        color: white;
    }
    .btn-disabled {
        background: #6c757d;
        color: white;
        cursor: not-allowed;
    }
    .loading-spinner {
        text-align: center;
        padding: 40px;
    }
    .no-tests {
        text-align: center;
        padding: 40px;
        color: #6c757d;
    }
    .pagination {
        justify-content: center;
        margin-top: 2rem;
    }
    .language-badges {
        display: flex;
        gap: 5px;
        margin-top: 5px;
    }
    .language-badge {
        background: #e9ecef;
        color: #495057;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
    }

    @media (max-width: 768px) {
        .ws_container {
            width: calc(100% - 4%);
        }
        .mockTestCard__header {
            flex-direction: column;
            gap: 10px;
        }
        .mockTestCard__details {
            grid-template-columns: 1fr;
            gap: 10px;
        }
        .mockTestCard__actions {
            justify-content: center;
        }
    }
</style>

<main>
    <div class="ws_container">
        <section class="liveMockTests">
            <h1 class="liveMockTests__title">Live Mock Tests</h1>

            <!-- Tab Navigation -->
            <ul class="nav nav-tabs" role="tablist">
                <li role="presentation" class="active">
                    <a href="#active-tests" aria-controls="active-tests" role="tab" data-toggle="tab" data-tab="active">
                        <i class="fa fa-play-circle"></i> Active Tests
                    </a>
                </li>
                <li role="presentation">
                    <a href="#completed-tests" aria-controls="completed-tests" role="tab" data-toggle="tab" data-tab="completed">
                        <i class="fa fa-check-circle"></i> Completed Tests
                    </a>
                </li>
                <li role="presentation">
                    <a href="#all-tests" aria-controls="all-tests" role="tab" data-toggle="tab" data-tab="all">
                        <i class="fa fa-list"></i> All Tests
                    </a>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content">
                <!-- Active Tests Tab -->
                <div role="tabpanel" class="tab-pane fade in active" id="active-tests">
                    <div id="active-tests-content" class="tests-content">
                        <div class="loading-spinner">
                            <i class="fa fa-spinner fa-spin fa-2x"></i>
                            <p>Loading active tests...</p>
                        </div>
                    </div>
                    <div id="active-tests-pagination"></div>
                </div>

                <!-- Completed Tests Tab -->
                <div role="tabpanel" class="tab-pane fade" id="completed-tests">
                    <div id="completed-tests-content" class="tests-content">
                        <div class="loading-spinner">
                            <i class="fa fa-spinner fa-spin fa-2x"></i>
                            <p>Loading completed tests...</p>
                        </div>
                    </div>
                    <div id="completed-tests-pagination"></div>
                </div>

                <!-- All Tests Tab -->
                <div role="tabpanel" class="tab-pane fade" id="all-tests">
                    <div id="all-tests-content" class="tests-content">
                        <div class="loading-spinner">
                            <i class="fa fa-spinner fa-spin fa-2x"></i>
                            <p>Loading all tests...</p>
                        </div>
                    </div>
                    <div id="all-tests-pagination"></div>
                </div>
            </div>
        </section>
    </div>
</main>

<script>
$(document).ready(function() {
    let currentTab = 'active';
    let currentPage = {
        active: 1,
        completed: 1,
        all: 1
    };
    const pageSize = 10;

    // Initialize the page
    loadTests('active', 1);

    // Handle tab switching
    $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
        const tabType = $(e.target).data('tab');
        currentTab = tabType;
        loadTests(tabType, currentPage[tabType]);
    });

    // Function to load tests based on tab type and page
    function loadTests(tabType, page = 1) {
        const contentId = tabType + '-tests-content';
        const paginationId = tabType + '-tests-pagination';

        // Show loading spinner
        $('#' + contentId).html(`
            <div class="loading-spinner">
                <i class="fa fa-spinner fa-spin fa-2x"></i>
                <p>Loading ${tabType} tests...</p>
            </div>
        `);

        // Clear pagination
        $('#' + paginationId).html('');

        let apiUrl;
        switch(tabType) {
            case 'active':
                apiUrl = '${createLink(controller: 'liveMockTests', action: 'getActiveMockTests')}';
                break;
            case 'completed':
                apiUrl = '${createLink(controller: 'liveMockTests', action: 'getCompletedMockTests')}';
                break;
            case 'all':
                apiUrl = '${createLink(controller: 'liveMockTests', action: 'getAllMockTests')}';
                break;
        }

        $.ajax({
            url: apiUrl,
            type: 'GET',
            data: {
                max: pageSize,
                page: page
            },
            success: function(response) {
                if (response.status === 'success') {
                    renderTests(response.data, contentId);
                    renderPagination(response.pagination, paginationId, tabType);
                    currentPage[tabType] = page;
                } else {
                    showError(contentId, response.message || 'Failed to load tests');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error loading tests:', error);
                showError(contentId, 'Failed to load tests. Please try again.');
            }
        });
    }

    // Function to render tests
    function renderTests(tests, contentId) {
        if (!tests || tests.length === 0) {
            $('#' + contentId).html(`
                <div class="no-tests">
                    <i class="fa fa-inbox fa-3x" style="color: #dee2e6; margin-bottom: 15px;"></i>
                    <h4>No tests found</h4>
                    <p>There are no tests available in this category.</p>
                </div>
            `);
            return;
        }

        let html = '';
        tests.forEach(function(test) {
            html += renderTestCard(test);
        });

        $('#' + contentId).html(html);
    }

    // Function to render individual test card
    function renderTestCard(test) {
        const statusClass = 'status-' + test.status;
        const cardClass = test.status;

        // Format dates
        const startDate = test.testStartDate ? formatDate(new Date(test.testStartDate)) : 'Not set';
        const endDate = test.testEndDate ? formatDate(new Date(test.testEndDate)) : 'Not set';

        // Language badges
        let languageBadges = '';
        if (test.language1) {
            languageBadges += '<span class="language-badge">' + test.language1 + '</span>';
        }
        if (test.language2) {
            languageBadges += '<span class="language-badge">' + test.language2 + '</span>';
        }

        // Action buttons based on status and access
        let actionButtons = '';
        if (test.hasTestAccess) {
            if (test.status === 'active') {
                actionButtons = '<a href="#" class="btn-test btn-start" onclick="startTest(' + test.mcqResId + ')"><i class="fa fa-play"></i> Start Test</a>';
            } else if (test.status === 'completed') {
                actionButtons = '<a href="#" class="btn-test btn-view" onclick="viewResults(' + test.mcqResId + ')"><i class="fa fa-eye"></i> View Results</a>';
            } else {
                actionButtons = '<span class="btn-test btn-disabled"><i class="fa fa-clock-o"></i> Upcoming</span>';
            }
        } else {
            actionButtons = '<span class="btn-test btn-disabled"><i class="fa fa-lock"></i> No Access</span>';
        }

        return `
            <div class="mockTestCard ${cardClass}">
                <div class="mockTestCard__header">
                    <div>
                        <div class="mockTestCard__title">${test.resourceName || 'Untitled Test'}</div>
                        <div class="language-badges">${languageBadges}</div>
                    </div>
                    <span class="mockTestCard__status ${statusClass}">${test.status}</span>
                </div>
                <div class="mockTestCard__details">
                    <div class="detail-item">
                        <i class="fa fa-question-circle"></i>
                        <span><strong>${test.mcqCount || 0}</strong> Questions</span>
                    </div>
                    <div class="detail-item">
                        <i class="fa fa-clock-o"></i>
                        <span><strong>${test.totalTime || 0}</strong> Minutes</span>
                    </div>
                    <div class="detail-item">
                        <i class="fa fa-calendar"></i>
                        <span><strong>Start:</strong> ${startDate}</span>
                    </div>
                    <div class="detail-item">
                        <i class="fa fa-calendar-times-o"></i>
                        <span><strong>End:</strong> ${endDate}</span>
                    </div>
                </div>
                <div class="mockTestCard__actions">
                    ${actionButtons}
                </div>
            </div>
        `;
    }

    // Function to render pagination
    function renderPagination(pagination, paginationId, tabType) {
        if (!pagination || pagination.totalPages <= 1) {
            $('#' + paginationId).html('');
            return;
        }

        const currentPage = pagination.currentPage;
        const totalPages = pagination.totalPages;

        let paginationHtml = '<ul class="pagination">';

        // Page info
        paginationHtml += '<li class="disabled"><span>Page ' + currentPage + ' of ' + totalPages + '</span></li>';

        // Previous button
        if (currentPage > 1) {
            paginationHtml += '<li><a href="#" data-page="' + (currentPage - 1) + '" data-tab="' + tabType + '">&laquo;</a></li>';
        } else {
            paginationHtml += '<li class="disabled"><span>&laquo;</span></li>';
        }

        // Page numbers
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        for (let i = startPage; i <= endPage; i++) {
            if (i === currentPage) {
                paginationHtml += '<li class="active"><span>' + i + '</span></li>';
            } else {
                paginationHtml += '<li><a href="#" data-page="' + i + '" data-tab="' + tabType + '">' + i + '</a></li>';
            }
        }

        // Next button
        if (currentPage < totalPages) {
            paginationHtml += '<li><a href="#" data-page="' + (currentPage + 1) + '" data-tab="' + tabType + '">&raquo;</a></li>';
        } else {
            paginationHtml += '<li class="disabled"><span>&raquo;</span></li>';
        }

        paginationHtml += '</ul>';
        $('#' + paginationId).html(paginationHtml);
    }

    // Handle pagination clicks
    $(document).on('click', '.pagination a', function(e) {
        e.preventDefault();
        const page = $(this).data('page');
        const tabType = $(this).data('tab');
        if (page && tabType) {
            loadTests(tabType, page);
        }
    });

    // Function to show error message
    function showError(contentId, message) {
        $('#' + contentId).html(`
            <div class="no-tests">
                <i class="fa fa-exclamation-triangle fa-3x" style="color: #dc3545; margin-bottom: 15px;"></i>
                <h4>Error</h4>
                <p>${message}</p>
                <button class="btn btn-primary" onclick="loadTests('${currentTab}', ${currentPage[currentTab]})">
                    <i class="fa fa-refresh"></i> Retry
                </button>
            </div>
        `);
    }

    // Utility function to format date
    function formatDate(date) {
        if (!date) return 'Not set';
        const options = {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };
        return date.toLocaleDateString('en-US', options);
    }

    // Function to start a test
    window.startTest = function(mcqResId) {
        // Implement test start logic here
        // This could redirect to a test page or open a modal
        console.log('Starting test with mcqResId:', mcqResId);
        // Example: window.location.href = '/test/start/' + mcqResId;
        alert('Test start functionality to be implemented. MCQ Resource ID: ' + mcqResId);
    };

    // Function to view test results
    window.viewResults = function(mcqResId) {
        // Implement results view logic here
        console.log('Viewing results for mcqResId:', mcqResId);
        // Example: window.location.href = '/test/results/' + mcqResId;
        alert('Results view functionality to be implemented. MCQ Resource ID: ' + mcqResId);
    };
});
</script>

<g:render template="/${session['entryController']}/footer_new"></g:render>