package com.wonderslate.data
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import grails.converters.JSON

class LiveMockTestsController {
    def springSecurityService
    def liveMockTestsService

    def index() { }

    @Secured(['ROLE_PDF_EXTRACTOR']) @Transactional
    def addMCQsToMockTest() {
        try {
            if(!params.mcqResId){
                render([status: "error", statusCode: 400, message: "MCQ Resource ID is required"] as JSON)
                return false
            }

            Long mcqResId = new Long(params.mcqResId)

            LiveMockMst liveMockMst = LiveMockMst.findByMcqResId(mcqResId)

            if(!liveMockMst){
                liveMockMst = new LiveMockMst(mcqResId: mcqResId, createdBy: springSecurityService.currentUser.username)
                liveMockMst.save(failOnError: true, flush: true)
            }else {
                if((liveMockMst.isDeleted == null || "false".equals(liveMockMst.isDeleted)) && isLiveMockActive(mcqResId)){
                    render([status: "error", statusCode: 400, message: "MCQs already added to mock test and is still active."] as JSON)
                    return false
                }
            }

            render([status: "success", statusCode: 200, message: "MCQs added to mock test"] as JSON)

        }catch (Exception e) {
            log.error("Error in addMCQsToMockTest: ${e.message}", e)
            render([status: "error", message: "Internal server error"] as JSON)
        }
    }

    private boolean isLiveMockActive(mcqResId) {
        boolean isLiveMockActive = false
        ResourceDtl resourceDtl = ResourceDtl.findById(mcqResId)
        if (resourceDtl!=null && resourceDtl.testStartDate!=null && resourceDtl.testEndDate!=null) {
            if(resourceDtl.testEndDate > new Date()){
                isLiveMockActive = true
            }
        }

        return isLiveMockActive
    }

    /**
     * API to list all active (ongoing) mock tests with pagination
     * To check if a test is active, we check if the test end date is greater than the current date and time along
     * with the test start date being less than the current date and time.
     * Supports pagination parameters: max (page size), offset or page (page number)
     */
    def getActiveMockTests() {
        try {
            // Handle pagination parameters
            int max = params.int('max') ?: 10
            int offset = params.int('offset') ?: 0

            // Support page parameter as alternative to offset
            if (params.page && !params.offset) {
                int page = params.int('page') ?: 1
                offset = (page - 1) * max
            }

            def paginationParams = [max: max, offset: offset]
            def result = liveMockTestsService.getActiveMockTests(paginationParams, session, request, response)

            if (result.status == 'success') {
                render([
                    status: "success",
                    statusCode: 200,
                    data: result.data,
                    pagination: result.pagination,
                    message: "Active mock tests retrieved successfully"
                ] as JSON)
            } else {
                render([status: "error", statusCode: 500, message: result.message] as JSON)
            }
        } catch (Exception e) {
            log.error("Error in getActiveMockTests: ${e.message}", e)
            render([status: "error", statusCode: 500, message: "Internal server error"] as JSON)
        }
    }

    /**
     * API to list all completed mock tests with pagination
     * To check if a test is completed, we check if the test end date is less than the current date and time.
     * Supports pagination parameters: max (page size), offset or page (page number)
     */
    def getCompletedMockTests() {
        try {
            // Handle pagination parameters
            int max = params.int('max') ?: 10
            int offset = params.int('offset') ?: 0

            // Support page parameter as alternative to offset
            if (params.page && !params.offset) {
                int page = params.int('page') ?: 1
                offset = (page - 1) * max
            }

            def paginationParams = [max: max, offset: offset]
            def result = liveMockTestsService.getCompletedMockTests(paginationParams)

            if (result.status == 'success') {
                render([
                    status: "success",
                    statusCode: 200,
                    data: result.data,
                    pagination: result.pagination,
                    message: "Completed mock tests retrieved successfully"
                ] as JSON)
            } else {
                render([status: "error", statusCode: 500, message: result.message] as JSON)
            }
        } catch (Exception e) {
            log.error("Error in getCompletedMockTests: ${e.message}", e)
            render([status: "error", statusCode: 500, message: "Internal server error"] as JSON)
        }
    }

    /**
     * API to list all tests with pagination
     * Supports pagination parameters: max (page size), offset or page (page number)
     */
    def getAllMockTests() {
        try {
            // Handle pagination parameters
            int max = params.int('max') ?: 10
            int offset = params.int('offset') ?: 0

            // Support page parameter as alternative to offset
            if (params.page && !params.offset) {
                int page = params.int('page') ?: 1
                offset = (page - 1) * max
            }

            def paginationParams = [max: max, offset: offset]
            def result = liveMockTestsService.getAllMockTests(paginationParams)

            if (result.status == 'success') {
                render([
                    status: "success",
                    statusCode: 200,
                    data: result.data,
                    pagination: result.pagination,
                    message: "All mock tests retrieved successfully"
                ] as JSON)
            } else {
                render([status: "error", statusCode: 500, message: result.message] as JSON)
            }
        } catch (Exception e) {
            log.error("Error in getAllMockTests: ${e.message}", e)
            render([status: "error", statusCode: 500, message: "Internal server error"] as JSON)
        }
    }

    /**
     * API to delete a mock test
     * To delete a mock test, we update the isDeleted field to true.
     */
    @Secured(['ROLE_PDF_EXTRACTOR']) @Transactional
    def deleteMockTest() {
        try {
            if (!params.mockTestId) {
                render([status: "error", statusCode: 400, message: "Mock test ID is required"] as JSON)
                return false
            }

            Long mockTestId = new Long(params.mockTestId)
            String deletedBy = springSecurityService.currentUser.username

            def result = liveMockTestsService.deleteMockTest(mockTestId, deletedBy)

            if (result.status == 'success') {
                render([status: "success", statusCode: 200, message: result.message] as JSON)
            } else {
                render([status: "error", statusCode: 400, message: result.message] as JSON)
            }
        } catch (Exception e) {
            log.error("Error in deleteMockTest: ${e.message}", e)
            render([status: "error", statusCode: 500, message: "Internal server error"] as JSON)
        }
    }


}
